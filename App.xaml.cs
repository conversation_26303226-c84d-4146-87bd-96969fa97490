﻿using System;
using System.Configuration;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;
using DriverManagementSystem.Migrations;
using OfficeOpenXml;

namespace DriverManagementSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private async void Application_Startup(object sender, StartupEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🚀 بدء تشغيل التطبيق...");

            // تكوين ترخيص EPPlus للاستخدام غير التجاري (EPPlus 8)
            ExcelPackage.License.SetNonCommercialPersonal("نظام إدارة الزيارات الميدانية - الصندوق الاجتماعي للتنمية");
            System.Diagnostics.Debug.WriteLine("✅ تم تكوين ترخيص EPPlus");

            // إضافة معالج للأخطاء غير المعالجة
            AppDomain.CurrentDomain.UnhandledException += (s, ex) =>
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ غير متوقع: {ex.ExceptionObject}");
                MessageBox.Show($"خطأ غير متوقع: {ex.ExceptionObject}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            };

            DispatcherUnhandledException += (s, ex) =>
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في واجهة المستخدم: {ex.Exception.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ نوع الخطأ: {ex.Exception.GetType().Name}");

                var errorMessage = $"خطأ في واجهة المستخدم:\n\n{ex.Exception.Message}";
                if (ex.Exception.InnerException != null)
                {
                    errorMessage += $"\n\nالخطأ الداخلي:\n{ex.Exception.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
                ex.Handled = true;

                // إذا كان الخطأ خطيراً، أغلق التطبيق
                if (ex.Exception is OutOfMemoryException || ex.Exception is StackOverflowException)
                {
                    Shutdown();
                }
            };

            System.Diagnostics.Debug.WriteLine("✅ تم تسجيل معالجات الأخطاء");

            // التحقق من وجود معامل لإضافة البيانات
            if (e.Args.Length > 0 && e.Args[0] == "--add-drivers")
            {
                System.Diagnostics.Debug.WriteLine("🔧 تشغيل وضع إضافة السائقين");
                await AddDriversData();
                Shutdown();
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔍 بدء فحص قاعدة البيانات والمستخدمين");
            // فحص قاعدة البيانات والمستخدمين
            await CheckDatabaseAndUsers();

            System.Diagnostics.Debug.WriteLine("🔧 إصلاح العمود VisitNumber");
            // إصلاح العمود VisitNumber في جدول DriverQuotes
            await ApplyVisitNumberColumnFix();

            System.Diagnostics.Debug.WriteLine("🧹 تنظيف رسائل السائق");
            // تنظيف رسائل السائق من مهام النزول
            await CleanDriverMessagesOnStartup();

            System.Diagnostics.Debug.WriteLine("✅ تم إكمال بدء التشغيل بنجاح");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في بدء تشغيل النظام: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
            MessageBox.Show($"🚨 خطأ في Application_Startup:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                "🔍 خطأ في بدء التشغيل!", MessageBoxButton.OK, MessageBoxImage.Error);
            MessageBox.Show("🚨 سيتم إغلاق النظام بسبب خطأ في Application_Startup", "إغلاق", MessageBoxButton.OK);
            Shutdown();
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        var stackTrace = new System.Diagnostics.StackTrace(true);
        System.Diagnostics.Debug.WriteLine("🚨 تم استدعاء إغلاق التطبيق!");
        System.Diagnostics.Debug.WriteLine($"📍 Exit Stack Trace:\n{stackTrace}");
        base.OnExit(e);
    }

    private async Task CheckDatabaseAndUsers()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔍 بدء فحص قاعدة البيانات والمستخدمين");

            // التحقق من وجود ملف إعدادات قاعدة البيانات
            if (!Data.DatabaseConfig.HasConfigFile())
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد ملف إعدادات قاعدة البيانات - إظهار نافذة الإعداد");

                var dbSetupWindow = new Views.DatabaseConfigWindow();
                var dbResult = dbSetupWindow.ShowDialog();

                if (dbResult != true)
                {
                    // التحقق مرة أخرى من وجود ملف إعدادات (ربما تم إنشاؤه من قبل)
                    if (!Data.DatabaseConfig.HasConfigFile())
                    {
                        MessageBox.Show("يجب إعداد قاعدة البيانات لتشغيل النظام", "إعداد مطلوب",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        Shutdown();
                        return;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم العثور على ملف إعدادات موجود مسبقاً");
                    }
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("✅ تم العثور على ملف إعدادات قاعدة البيانات");
            }

            // محاولة الاتصال بقاعدة البيانات باستخدام النظام الآمن
            bool canConnect = await Data.DatabaseConfig.CanConnectToDatabase();
            System.Diagnostics.Debug.WriteLine($"📡 حالة الاتصال بقاعدة البيانات: {(canConnect ? "✅ متصل" : "❌ غير متصل")}");

            // إذا فشل الاتصال، محاولة إعداد قاعدة البيانات تلقائياً أولاً
            if (!canConnect)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لا يمكن الاتصال بقاعدة البيانات - محاولة الإعداد التلقائي...");

                // محاولة إعداد قاعدة البيانات تلقائياً
                var autoSetup = await Data.DatabaseConfig.SafeSetupDatabase();
                if (autoSetup)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم إعداد قاعدة البيانات تلقائياً");
                    canConnect = true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل الإعداد التلقائي - إظهار نافذة الإعداد");

                    var dbSetupWindow = new Views.DatabaseConfigWindow();
                    var dbResult = dbSetupWindow.ShowDialog();

                    if (dbResult != true)
                    {
                        // التحقق من وجود إعدادات صحيحة
                        if (Data.DatabaseConfig.HasConfigFile())
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ المستخدم ألغى نافذة الإعداد ولكن توجد إعدادات محفوظة");
                            // المتابعة مع الإعدادات الموجودة
                        }
                        else
                        {
                            MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. تأكد من إعدادات الاتصال.", "خطأ في الاتصال",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            Shutdown();
                            return;
                        }
                    }

                    // إعادة فحص الاتصال بعد الإعداد اليدوي
                    canConnect = await Data.DatabaseConfig.CanConnectToDatabase();
                    if (!canConnect)
                    {
                        MessageBox.Show("لا يزال لا يمكن الاتصال بقاعدة البيانات بعد الإعداد.", "خطأ في الاتصال",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        Shutdown();
                        return;
                    }
                }
            }

            // الآن نحن متأكدون من الاتصال بقاعدة البيانات
            // المرحلة التالية: فحص وجود المستخدمين
            await SetupDatabaseAndShowNextWindow();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص قاعدة البيانات: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
            MessageBox.Show($"🚨 خطأ في CheckDatabaseAndUsers:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                "🔍 خطأ في فحص قاعدة البيانات!", MessageBoxButton.OK, MessageBoxImage.Error);
            MessageBox.Show("🚨 سيتم إغلاق النظام بسبب خطأ في CheckDatabaseAndUsers", "إغلاق", MessageBoxButton.OK);
            Shutdown();
        }
    }

    /// <summary>
    /// إعداد قاعدة البيانات وإظهار النافذة التالية حسب حالة النظام
    /// </summary>
    private async Task SetupDatabaseAndShowNextWindow()
    {
        try
        {
            // إعداد قاعدة البيانات بشكل آمن
            var dbSetup = await Data.DatabaseConfig.SafeSetupDatabase();
            if (!dbSetup)
            {
                MessageBox.Show("فشل في إعداد قاعدة البيانات. يرجى التحقق من الإعدادات.", "خطأ في قاعدة البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
                return;
            }

            // إنشاء DbContext مع إعدادات قاعدة البيانات
            var connectionString = Data.DatabaseConfig.GetConnectionString();
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(connectionString)
                .Options;

            using var context = new ApplicationDbContext(options);

            // التأكد من إنشاء الجداول فقط (قاعدة البيانات موجودة بالفعل)
            await context.Database.EnsureCreatedAsync();

            // التحقق من وجود جدول FieldVisitItineraries وإنشاؤه فقط إذا لم يكن موجوداً
            await EnsureFieldVisitItinerariesTableExists(context);

            // فحص وجود مستخدمين
            var usersCount = await context.Users.CountAsync();

            if (usersCount == 0)
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ لا يوجد مستخدمين - إظهار نافذة إنشاء المستخدم الأول");

                // لا يوجد مستخدمين - إظهار نافذة إنشاء المستخدم الأول
                var setupWindow = new Views.InitialSetupWindow();
                var result = setupWindow.ShowDialog();

                if (result != true)
                {
                    // المستخدم ألغى الإعداد - إغلاق النظام
                    Shutdown();
                    return;
                }

                // إضافة البيانات الأساسية بعد إنشاء المستخدم الأول
                var seeder = new InitialDataSeeder(context);
                await seeder.SeedBasicDataAsync();

                // إدراج بيانات السائقين الأولية
                await Data.SeedDriversData.SeedDriversAsync(context);

                System.Diagnostics.Debug.WriteLine("✅ تم إعداد النظام بنجاح - الانتقال لنافذة تسجيل الدخول");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {usersCount} مستخدم - الانتقال لنافذة تسجيل الدخول");

                // حتى لو كان هناك مستخدمين، تأكد من إدراج بيانات السائقين
                await Data.SeedDriversData.SeedDriversAsync(context);
            }

            // إظهار نافذة تسجيل الدخول
            System.Diagnostics.Debug.WriteLine("🔐 إظهار نافذة تسجيل الدخول");
            var loginWindow = new Views.LoginWindow();
            var loginResult = loginWindow.ShowDialog();

            System.Diagnostics.Debug.WriteLine($"🔐 نتيجة تسجيل الدخول: {loginResult}");

            if (loginResult == true)
            {
                // تم تسجيل الدخول بنجاح - النافذة الرئيسية تم إنشاؤها بالفعل في LoginWindow
                System.Diagnostics.Debug.WriteLine("✅ تم تسجيل الدخول بنجاح - النافذة الرئيسية تم إنشاؤها بالفعل");
            }
            else
            {
                // المستخدم ألغى تسجيل الدخول - إغلاق النظام
                System.Diagnostics.Debug.WriteLine("❌ تم إلغاء تسجيل الدخول - إغلاق النظام");
                Shutdown();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد النظام: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
            MessageBox.Show($"خطأ في إعداد النظام: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.ToString()}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private async Task AddDriversData()
    {
        try
        {
            Console.WriteLine("🔄 بدء إضافة بيانات السائقين والمركبات والمشاريع...");

            var seeder = new Services.DatabaseSeeder();
            await seeder.SeedAllDataAsync();

            Console.WriteLine("✅ تم إضافة جميع البيانات بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
        }
    }



    /// <summary>
    /// التحقق من وجود جدول FieldVisitItineraries وإنشاؤه فقط إذا لم يكن موجوداً
    /// </summary>
    private async Task EnsureFieldVisitItinerariesTableExists(ApplicationDbContext context)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔍 فحص وجود جدول FieldVisitItineraries...");

            // التحقق من وجود الجدول
            var tableExists = false;
            try
            {
                // استخدام طريقة أفضل للتحقق من وجود الجدول
                var tableExistsQuery = @"
                    SELECT CASE WHEN EXISTS (
                        SELECT * FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_NAME = 'FieldVisitItineraries'
                    ) THEN 1 ELSE 0 END";

                var connection = context.Database.GetDbConnection();
                var wasOpen = connection.State == System.Data.ConnectionState.Open;

                if (!wasOpen)
                    await context.Database.OpenConnectionAsync();

                using var command = connection.CreateCommand();
                command.CommandText = tableExistsQuery;
                var result = await command.ExecuteScalarAsync();
                tableExists = Convert.ToInt32(result) == 1;

                if (!wasOpen)
                    await context.Database.CloseConnectionAsync();
            }
            catch
            {
                // إذا فشل الاستعلام، نفترض أن الجدول غير موجود
                tableExists = false;
            }

            if (tableExists)
            {
                System.Diagnostics.Debug.WriteLine("✅ جدول FieldVisitItineraries موجود مسبقاً - لن يتم إعادة إنشاؤه");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔧 إنشاء جدول FieldVisitItineraries...");

            // إنشاء الجدول الجديد
            var createTableSql = @"
                CREATE TABLE [FieldVisitItineraries] (
                    [Id] int IDENTITY(1,1) NOT NULL,
                    [FieldVisitId] int NOT NULL,
                    [DayNumber] int NOT NULL,
                    [ItineraryText] nvarchar(1000) NOT NULL,
                    [CreatedAt] datetime2 NOT NULL DEFAULT (GETDATE()),
                    [UpdatedAt] datetime2 NULL,
                    [Notes] nvarchar(500) NULL,
                    [IsActive] bit NOT NULL DEFAULT (1),
                    CONSTRAINT [PK_FieldVisitItineraries] PRIMARY KEY ([Id])
                );";

            await context.Database.ExecuteSqlRawAsync(createTableSql);
            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول FieldVisitItineraries");

            // إضافة Foreign Key إذا كان جدول FieldVisits موجود
            var addForeignKeySql = @"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisits')
                BEGIN
                    ALTER TABLE [FieldVisitItineraries]
                    ADD CONSTRAINT [FK_FieldVisitItineraries_FieldVisits]
                    FOREIGN KEY ([FieldVisitId]) REFERENCES [FieldVisits] ([Id]) ON DELETE CASCADE;
                END";

            await context.Database.ExecuteSqlRawAsync(addForeignKeySql);
            System.Diagnostics.Debug.WriteLine("🔗 تم إضافة Foreign Key");

            // إضافة الفهارس
            var addIndexesSql = @"
                CREATE INDEX [IX_FieldVisitItineraries_FieldVisitId] ON [FieldVisitItineraries] ([FieldVisitId]);
                CREATE INDEX [IX_FieldVisitItineraries_IsActive] ON [FieldVisitItineraries] ([IsActive]);
                CREATE UNIQUE INDEX [IX_FieldVisitItineraries_FieldVisitId_DayNumber] ON [FieldVisitItineraries] ([FieldVisitId], [DayNumber]);";

            await context.Database.ExecuteSqlRawAsync(addIndexesSql);
            System.Diagnostics.Debug.WriteLine("📊 تم إضافة الفهارس");

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول FieldVisitItineraries بنجاح!");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول FieldVisitItineraries: {ex.Message}");
            // لا نرمي الخطأ لأن هذا قد يوقف تشغيل النظام
        }
    }

    /// <summary>
    /// تنظيف رسائل السائق من مهام النزول عند بدء التطبيق
    /// </summary>
    private async Task CleanDriverMessagesOnStartup()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🧹 بدء تنظيف رسائل السائق من مهام النزول...");

            var dataService = new DatabaseService();
            var result = await dataService.CleanAllDriverMessagesAsync();

            if (result)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تنظيف رسائل السائق بنجاح");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على رسائل سائق للتنظيف");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف رسائل السائق: {ex.Message}");
            // لا نوقف التطبيق بسبب هذا الخطأ
        }
    }

    /// <summary>
    /// إصلاح العمود VisitNumber في جدول DriverQuotes
    /// </summary>
    private async Task ApplyVisitNumberColumnFix()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔧 بدء إصلاح العمود VisitNumber");

            var connectionString = Data.DatabaseConfig.GetConnectionString();
            if (string.IsNullOrEmpty(connectionString))
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد اتصال بقاعدة البيانات");
                return;
            }

            // تم إزالة migration العمود VisitNumber
            System.Diagnostics.Debug.WriteLine("ℹ️ تم تخطي إصلاح العمود VisitNumber");

            // حذف الأعمدة غير المستخدمة
            var removeColumnsSuccess = await SFDSystem.Migrations.RemoveUnusedDriverQuoteColumns.ApplyAsync(connectionString);
            if (removeColumnsSuccess)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم حذف الأعمدة غير المستخدمة بنجاح");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ فشل في حذف الأعمدة غير المستخدمة");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح العمود VisitNumber: {ex.Message}");
        }
    }

}


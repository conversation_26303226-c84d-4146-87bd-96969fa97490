using System;
using System.Globalization;
using System.Windows.Data;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// محول لتغيير نص التلميح لزر الرسالة بناءً على حالة اختيار السائقين
    /// </summary>
    public class BoolToMessageTooltipConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool hasSelectedDrivers)
            {
                return hasSelectedDrivers 
                    ? "تم اختيار سائقين لهذه الزيارة - اضغط لإدارة الرسائل"
                    : "إنشاء رسالة واختيار سائقين للزيارة";
            }
            
            return "إنشاء رسالة للسائق";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

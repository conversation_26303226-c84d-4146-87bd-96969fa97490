-- إضافة الأعمدة الجديدة لجدول السائقين
-- تاريخ انتهاء الرخصة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'LicenseExpiryDate')
BEGIN
    ALTER TABLE Drivers ADD LicenseExpiryDate datetime2 NULL;
    PRINT 'تم إضافة عمود LicenseExpiryDate';
END
ELSE
BEGIN
    PRINT 'عمود LicenseExpiryDate موجود مسبقاً';
END

-- رقم رخصة القيادة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'DrivingLicenseNumber')
BEGIN
    ALTER TABLE Drivers ADD DrivingLicenseNumber nvarchar(50) NULL;
    PRINT 'تم إضافة عمود DrivingLicenseNumber';
END
ELSE
BEGIN
    PRINT 'عمود DrivingLicenseNumber موجود مسبقاً';
END

-- تاريخ انتهاء رخصة القيادة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'DrivingLicenseExpiryDate')
BEGIN
    ALTER TABLE Drivers ADD DrivingLicenseExpiryDate datetime2 NULL;
    PRINT 'تم إضافة عمود DrivingLicenseExpiryDate';
END
ELSE
BEGIN
    PRINT 'عمود DrivingLicenseExpiryDate موجود مسبقاً';
END

-- التحقق من الأعمدة المضافة
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Drivers' 
    AND COLUMN_NAME IN ('LicenseExpiryDate', 'DrivingLicenseNumber', 'DrivingLicenseExpiryDate')
ORDER BY COLUMN_NAME;

PRINT 'تم الانتهاء من إضافة الأعمدة الجديدة';

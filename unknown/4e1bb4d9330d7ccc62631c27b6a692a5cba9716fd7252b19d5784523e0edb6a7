using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج عرض السعر للسائق - مخصص لنافذة العروض
    /// </summary>
    public class DriverOffer : INotifyPropertyChanged
    {
        private bool _isSelected;
        private decimal _proposedAmount;
        private int _daysCount;
        private bool _isWinner;
        private string _offerStatus = "في الانتظار";

        public int DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public string DriverCode { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string VehicleNumber { get; set; } = string.Empty;
        public string VisitNumber { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// عدد الأيام (ثابت حسب رقم الزيارة)
        /// </summary>
        public int DaysCount
        {
            get => _daysCount;
            set
            {
                if (_daysCount != value)
                {
                    _daysCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DailyRate));
                    OnPropertyChanged(nameof(FormattedDailyRate));
                }
            }
        }

        /// <summary>
        /// المبلغ المقترح (قابل للتحرير)
        /// </summary>
        public decimal ProposedAmount
        {
            get => _proposedAmount;
            set
            {
                if (_proposedAmount != value)
                {
                    _proposedAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAmount));
                    OnPropertyChanged(nameof(DailyRate));
                    OnPropertyChanged(nameof(FormattedDailyRate));
                }
            }
        }

        /// <summary>
        /// إجمالي اليوم (محسوب تلقائياً)
        /// </summary>
        public decimal DailyRate => DaysCount > 0 ? ProposedAmount / DaysCount : 0;

        /// <summary>
        /// هل هذا السائق فائز؟
        /// </summary>
        public bool IsWinner
        {
            get => _isWinner;
            set
            {
                if (_isWinner != value)
                {
                    _isWinner = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(WinnerIndicator));

                    // تحديث حالة العرض تلقائياً
                    if (value && _offerStatus != "🏆 فائز")
                    {
                        _offerStatus = "🏆 فائز";
                        OnPropertyChanged(nameof(OfferStatus));
                    }
                    else if (!value && _offerStatus == "🏆 فائز")
                    {
                        _offerStatus = "في الانتظار";
                        OnPropertyChanged(nameof(OfferStatus));
                    }
                }
            }
        }

        /// <summary>
        /// مؤشر الفوز
        /// </summary>
        public string WinnerIndicator => IsWinner ? "🏆" : "";

        /// <summary>
        /// اختيار/عدم اختيار
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حالة العرض (قابلة للتعديل)
        /// </summary>
        public string OfferStatus
        {
            get => _offerStatus;
            set
            {
                if (_offerStatus != value)
                {
                    _offerStatus = value;
                    OnPropertyChanged();

                    // تحديث حالة الفوز تلقائياً
                    if (value == "🏆 فائز")
                    {
                        if (!_isWinner)
                        {
                            _isWinner = true;
                            OnPropertyChanged(nameof(IsWinner));
                            OnPropertyChanged(nameof(WinnerIndicator));
                        }
                    }
                    else if (_isWinner && value != "🏆 فائز")
                    {
                        _isWinner = false;
                        OnPropertyChanged(nameof(IsWinner));
                        OnPropertyChanged(nameof(WinnerIndicator));
                    }

                    // إذا كانت الحالة "اعتذر"، قم بإلغاء الفوز فقط (احتفظ بالتحديد لحفظ الحالة)
                    if (value == "😔 اعتذر" || value == "اعتذر")
                    {
                        if (_isWinner)
                        {
                            _isWinner = false;
                            OnPropertyChanged(nameof(IsWinner));
                            OnPropertyChanged(nameof(WinnerIndicator));
                        }
                        // الاحتفاظ بالتحديد لضمان حفظ حالة الاعتذار
                        if (!_isSelected)
                        {
                            _isSelected = true;
                            OnPropertyChanged(nameof(IsSelected));
                        }
                    }
                }
            }
        }

        // خصائص منسقة للعرض
        public string FormattedAmount => $"{ProposedAmount:N0} ريال";
        public string FormattedDailyRate => $"{DailyRate:N0} ريال/يوم";
        public string DriverInfo => $"{DriverName} ({DriverCode})";
        public string ContactInfo => $"{PhoneNumber}";
        public string VehicleInfo => string.IsNullOrEmpty(VehicleType) ? "غير محدد" : $"{VehicleType} - {VehicleNumber}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// إنشاء عرض من سائق
        /// </summary>
        public static DriverOffer FromDriver(Driver driver, int daysCount = 1)
        {
            return new DriverOffer
            {
                DriverId = driver.Id,
                DriverName = driver.Name,
                DriverCode = driver.DriverCode,
                PhoneNumber = driver.PhoneNumber ?? string.Empty,
                VehicleType = driver.VehicleType ?? string.Empty,
                VehicleNumber = driver.VehicleNumber ?? string.Empty,
                DaysCount = daysCount,
                ProposedAmount = driver.QuotedPrice ?? 0,
                IsSelected = false,
                IsWinner = false
            };
        }

        /// <summary>
        /// تحويل إلى نص للحفظ
        /// </summary>
        public string ToSaveString()
        {
            return $"{DriverName} - {FormattedAmount}";
        }

        /// <summary>
        /// نسخ البيانات إلى DriverQuote
        /// </summary>
        public DriverQuote ToDriverQuote()
        {
            // تحديد الحالة بناءً على OfferStatus
            QuoteStatus status = QuoteStatus.Pending;

            if (OfferStatus == "😔 اعتذر" || OfferStatus == "اعتذر")
                status = QuoteStatus.Apologized;
            else if (OfferStatus == "🏆 فائز" || IsWinner)
                status = QuoteStatus.Accepted;
            else if (OfferStatus == "✅ معتمد")
                status = QuoteStatus.Accepted;
            else if (OfferStatus == "مرفوض")
                status = QuoteStatus.Rejected;
            else if (OfferStatus == "قيد المراجعة")
                status = QuoteStatus.UnderReview;
            else if (IsSelected && status == QuoteStatus.Pending)
                status = QuoteStatus.Accepted;

            return new DriverQuote
            {
                DriverId = this.DriverId,
                DriverName = this.DriverName,
                DriverCode = this.DriverCode,
                PhoneNumber = this.PhoneNumber,
                VehicleType = this.VehicleType,
                VehicleNumber = this.VehicleNumber,
                VisitNumber = this.VisitNumber, // إضافة رقم الزيارة
                QuotedPrice = this.ProposedAmount,
                QuotedDays = this.DaysCount,
                QuoteDate = DateTime.Now,
                Status = status,
                OfferStatus = this.OfferStatus, // حفظ الحالة النصية
                Notes = status == QuoteStatus.Apologized ? $"Visit: {VisitNumber}-اعتذر" :
                       IsWinner ? $"Visit: {VisitNumber}-فائز" : $"Visit: {VisitNumber}"
            };
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(DriverName) && 
                   ProposedAmount > 0 && 
                   DaysCount > 0;
        }

        /// <summary>
        /// نسخ البيانات من عرض آخر
        /// </summary>
        public void CopyFrom(DriverOffer other)
        {
            if (other == null) return;

            DriverId = other.DriverId;
            DriverName = other.DriverName;
            DriverCode = other.DriverCode;
            PhoneNumber = other.PhoneNumber;
            VehicleType = other.VehicleType;
            VehicleNumber = other.VehicleNumber;
            DaysCount = other.DaysCount;
            ProposedAmount = other.ProposedAmount;
            IsSelected = other.IsSelected;
            IsWinner = other.IsWinner;
        }

        public override string ToString()
        {
            return $"{DriverName} - {FormattedAmount} ({FormattedDailyRate})";
        }

        public override bool Equals(object? obj)
        {
            return obj is DriverOffer offer && DriverId == offer.DriverId;
        }

        public override int GetHashCode()
        {
            return DriverId.GetHashCode();
        }
    }
}

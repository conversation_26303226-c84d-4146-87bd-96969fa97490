<Window x:Class="DriverManagementSystem.Views.VehicleManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة السيارات والسائقين"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="90"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="0,0,10,10">
            <Grid>
                <TextBlock Text="🚗 إدارة السيارات والسائقين"
                          FontSize="22" FontWeight="Bold" Foreground="White"
                          HorizontalAlignment="Left" VerticalAlignment="Center" Margin="50,0,0,0"/>
            </Grid>
        </Border>

        <!-- Main Content with Tabs -->
        <TabControl x:Name="MainTabControl" Grid.Row="1" Margin="10,10,10,0" Background="Transparent" BorderThickness="0" VerticalAlignment="Stretch">
            <TabControl.Resources>
                <!-- Tab Header Style -->
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Border" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1,1,1,0"
                                        CornerRadius="8,8,0,0" Margin="2,0" Padding="20,12">
                                    <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center"
                                                    ContentSource="Header" Margin="0" RecognizesAccessKey="True"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#2196F3"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#BBDEFB"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="FontSize" Value="14"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                </Style>
            </TabControl.Resources>

            <!-- Tab 1: Drivers List -->
            <TabItem Header="📋 بيانات السائقين">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Compact Header with Statistics -->
                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="قائمة السيارات والسائقين" FontSize="14" FontWeight="Bold"
                                      Foreground="#2196F3" VerticalAlignment="Center"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="إجمالي: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,3,0" FontSize="11"/>
                                <TextBlock Text="{Binding TotalDriversCount}" FontWeight="Bold" Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="11"/>
                                <TextBlock Text="المعروض: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,3,0" FontSize="11"/>
                                <TextBlock Text="{Binding FilteredDriversCount}" FontWeight="Bold" Foreground="#2196F3" VerticalAlignment="Center" FontSize="11"/>
                            </StackPanel>
                        </Grid>

                        <!-- Compact Filter Section -->
                        <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="6" Padding="10" Margin="0,0,0,8"
                                BorderBrush="#E0E0E0" BorderThickness="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Compact Search Header -->
                                <TextBlock Grid.Row="0" Text="🔍 البحث والفلترة" FontSize="12" FontWeight="Bold"
                                          Foreground="#495057" Margin="0,0,0,8"/>

                                <!-- Search Controls -->
                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Compact Search Box -->
                                    <Border Grid.Column="0" Background="White" CornerRadius="4" Margin="0,0,8,0"
                                            BorderBrush="#CED4DA" BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="🔍" FontSize="12" VerticalAlignment="Center"
                                                      Margin="8,0,6,0" Foreground="#6C757D"/>
                                            <TextBox Grid.Column="1" x:Name="SearchTextBox"
                                                    Background="Transparent" BorderThickness="0"
                                                    FontSize="12" VerticalAlignment="Center" Height="30"
                                                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}">
                                                <TextBox.Style>
                                                    <Style TargetType="TextBox">
                                                        <Style.Triggers>
                                                            <Trigger Property="Text" Value="">
                                                                <Setter Property="Background">
                                                                    <Setter.Value>
                                                                        <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                                            <VisualBrush.Visual>
                                                                                <Label Content="البحث بالاسم، الهاتف، رقم المركبة..."
                                                                                       Foreground="#999" FontSize="11"/>
                                                                            </VisualBrush.Visual>
                                                                        </VisualBrush>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBox.Style>
                                            </TextBox>
                                        </Grid>
                                    </Border>

                                    <!-- Compact Vehicle Type Filter -->
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding VehicleTypesFilter}"
                                             SelectedItem="{Binding SelectedVehicleTypeFilter}"
                                             Height="30" FontSize="11" Margin="0,0,8,0"
                                             Background="White" BorderBrush="#CED4DA"/>

                                    <!-- Compact Vehicle Capacity Filter -->
                                    <ComboBox Grid.Column="2" ItemsSource="{Binding VehicleCapacitiesFilter}"
                                             SelectedItem="{Binding SelectedVehicleCapacityFilter}"
                                             Height="30" FontSize="11" Margin="0,0,8,0"
                                             Background="White" BorderBrush="#CED4DA"/>

                                    <!-- Compact Filter Button -->
                                    <Button Grid.Column="3" Content="🗑️ مسح" Height="30" Width="60"
                                           Background="#6C757D" Foreground="White" BorderThickness="0"
                                           FontSize="10" FontWeight="Bold"
                                           Click="ClearFilter_Click">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Button.Template>
                                    </Button>
                                </Grid>
                            </Grid>
                        </Border>

                        <!-- Expanded Drivers Data Grid Section -->
                        <Border Grid.Row="2" Background="White" CornerRadius="10" Padding="20" Margin="0,8,0,8"
                                BorderBrush="#C0C0C0" BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="320" ShadowDepth="4" Opacity="0.4"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Expanded Data Grid -->
                                <DataGrid Grid.Row="0" x:Name="DriversDataGrid"
                                         ItemsSource="{Binding FilteredDrivers}"
                                         SelectedItem="{Binding SelectedDriver}"
                                         AutoGenerateColumns="False"
                                         CanUserAddRows="False"
                                         CanUserDeleteRows="False"
                                         GridLinesVisibility="Horizontal"
                                         HeadersVisibility="Column"
                                         SelectionMode="Single"
                                         AlternatingRowBackground="#F8F9FA"
                                         RowBackground="White"
                                         FontSize="11"
                                         MinHeight="450"
                                         Margin="0">

                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="م" Binding="{Binding Id}" Width="50" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="140" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="90" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم المركبة" Binding="{Binding VehicleNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="رقم الهوية" Binding="{Binding CardNumber}" Width="110" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="نوع الهوية" Binding="{Binding CardType}" Width="100" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="لون المركبة" Binding="{Binding VehicleColor}" Width="90" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="قدرة المركبة" Binding="{Binding VehicleCapacity}" Width="90" IsReadOnly="True"/>

                                        <!-- License Status Column with Color Coding -->
                                        <DataGridTemplateColumn Header="حالة الرخصة" Width="100" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding LicenseStatus}" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding LicenseStatus}" Value="منتهية الصلاحية">
                                                                        <Setter Property="Foreground" Value="Red"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding LicenseStatus}" Value="تنتهي قريباً">
                                                                        <Setter Property="Foreground" Value="Orange"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding LicenseStatus}" Value="سارية">
                                                                        <Setter Property="Foreground" Value="Green"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>

                                        <!-- Driving License Status Column with Color Coding -->
                                        <DataGridTemplateColumn Header="حالة رخصة القيادة" Width="120" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding DrivingLicenseStatus}" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding DrivingLicenseStatus}" Value="منتهية الصلاحية">
                                                                        <Setter Property="Foreground" Value="Red"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding DrivingLicenseStatus}" Value="تنتهي قريباً">
                                                                        <Setter Property="Foreground" Value="Orange"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                    <DataTrigger Binding="{Binding DrivingLicenseStatus}" Value="سارية">
                                                                        <Setter Property="Foreground" Value="Green"/>
                                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>

                                        <!-- Action Buttons Column -->
                                        <DataGridTemplateColumn Header="الإجراءات" Width="120" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                               Background="#17A2B8" Foreground="White" BorderThickness="0"
                                                               ToolTip="عرض تفاصيل السائق" FontSize="10"
                                                               Click="ViewDriverButton_Click" Tag="{Binding}"/>
                                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                               Background="#FFC107" Foreground="#212529" BorderThickness="0"
                                                               ToolTip="تعديل بيانات السائق" FontSize="10"
                                                               Click="EditDriverButton_Click" Tag="{Binding}"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>

                                    <DataGrid.RowStyle>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="Height" Value="35"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#2196F3"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.RowStyle>

                                    <DataGrid.ColumnHeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#37474F"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Height" Value="40"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGrid.ColumnHeaderStyle>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- Tab 2: Add/Edit Driver -->
            <TabItem Header="➕ إضافة سائق">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Driver Information Section -->
                        <Border Grid.Row="0" Background="White" CornerRadius="8" Margin="5" Padding="15"
                                BorderBrush="#E0E0E0" BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>

                            <StackPanel>
                                <TextBlock Text="بيانات السائق" FontSize="18" FontWeight="Bold"
                                          Foreground="#2196F3" Margin="0,0,0,15"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Row 1 -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="اسم مالك السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding DriverName, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="رقم التلفون" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="رقم البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding CardNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <!-- Row 2 -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="نوع البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <ComboBox ItemsSource="{Binding CardTypes}"
                                                 SelectedItem="{Binding SelectedCardType}"
                                                 Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="مكان الإصدار للبطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding CardIssuePlace, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="تاريخ اصدار البطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <DatePicker SelectedDate="{Binding CardIssueDate}"
                                                   Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <!-- Row 3 -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="كود السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding DriverCode, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12" IsReadOnly="True" Background="#F5F5F5"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                        <TextBlock Text="حالة السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <ToggleButton IsChecked="{Binding IsActive, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                                     Height="35" FontSize="12" FontWeight="Bold">
                                            <ToggleButton.Style>
                                                <Style TargetType="ToggleButton">
                                                    <Setter Property="Content" Value="غير نشط"/>
                                                    <Setter Property="Background" Value="#F44336"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="BorderBrush" Value="Transparent"/>
                                                    <Style.Triggers>
                                                        <Trigger Property="IsChecked" Value="True">
                                                            <Setter Property="Content" Value="نشط"/>
                                                            <Setter Property="Background" Value="#4CAF50"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ToggleButton.Style>
                                        </ToggleButton>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Vehicle Information Section -->
                        <Border Grid.Row="1" Background="White" CornerRadius="8" Margin="5" Padding="15"
                                BorderBrush="#E0E0E0" BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>

                            <StackPanel>
                                <TextBlock Text="بيانات السائق" FontSize="18" FontWeight="Bold"
                                          Foreground="#2196F3" Margin="0,0,0,15"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Row 1 -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="نوع السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <ComboBox ItemsSource="{Binding VehicleTypes}"
                                                 SelectedItem="{Binding SelectedVehicleType}"
                                                 Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="رقم السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding VehicleNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="موديل السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding VehicleModel, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <!-- Row 2 -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="قدرة السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <ComboBox ItemsSource="{Binding VehicleCapacities}"
                                                 SelectedItem="{Binding SelectedVehicleCapacity}"
                                                 Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="رقم الرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding LicenseNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="تاريخ الاصدار للرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <DatePicker SelectedDate="{Binding LicenseIssueDate}"
                                                   Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <!-- Row 3 - License Expiry and Driving License -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="تاريخ انتهاء الرخصة" FontWeight="Bold" Margin="0,0,0,5" Foreground="#FF5722"/>
                                        <DatePicker SelectedDate="{Binding LicenseExpiryDate}"
                                                   Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                        <TextBlock Text="رقم رخصة القيادة" FontWeight="Bold" Margin="0,0,0,5" Foreground="#FF5722"/>
                                        <TextBox Text="{Binding DrivingLicenseNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="2" Grid.Column="2" Margin="5">
                                        <TextBlock Text="تاريخ انتهاء رخصة القيادة" FontWeight="Bold" Margin="0,0,0,5" Foreground="#FF5722"/>
                                        <DatePicker SelectedDate="{Binding DrivingLicenseExpiryDate}"
                                                   Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <!-- Row 4 - Additional Fields -->
                                    <StackPanel Grid.Row="3" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <ComboBox ItemsSource="{Binding VehicleColors}"
                                                 SelectedItem="{Binding SelectedVehicleColor}"
                                                 Height="35" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Margin="5">
                                        <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                                Height="60" FontSize="12" TextWrapping="Wrap"
                                                AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Action Buttons for Add Driver Tab -->
                        <Border Grid.Row="2" Background="White" CornerRadius="8" Margin="5" Padding="15"
                                BorderBrush="#E0E0E0" BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>

                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="💾 حفظ السائق"
                                       Height="45" MinWidth="140" Margin="8" FontSize="14" FontWeight="Bold"
                                       Background="#28A745" Foreground="White" BorderBrush="Transparent"
                                       Command="{Binding SaveCommand}"
                                       ToolTip="حفظ بيانات السائق الجديد"/>

                                <Button Content="🔄 مسح النموذج"
                                       Height="45" MinWidth="140" Margin="8" FontSize="14" FontWeight="Bold"
                                       Background="#FFC107" Foreground="#212529" BorderBrush="Transparent"
                                       Click="ClearFormButton_Click"
                                       ToolTip="مسح جميع البيانات المدخلة"/>

                                <Button Content="📋 عرض القائمة"
                                       Height="45" MinWidth="140" Margin="8" FontSize="14" FontWeight="Bold"
                                       Background="#17A2B8" Foreground="White" BorderBrush="Transparent"
                                       Click="ShowDriversListButton_Click"
                                       ToolTip="الانتقال إلى قائمة السائقين"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Action Buttons - Fixed at Bottom -->
        <Border Grid.Row="2" Background="#FFFFFF" BorderBrush="#D0D0D0" BorderThickness="0,2,0,0" Padding="15" Margin="0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="90" ShadowDepth="2" Opacity="0.25"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="➕ إضافة سائق"
                       Height="40" MinWidth="115" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#28A745" Foreground="White" BorderBrush="#1E7E34" BorderThickness="1"
                       Click="AddNewDriverButton_Click"
                       ToolTip="إضافة سائق جديد">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="5"
                                   Padding="6,4" BorderBrush="{TemplateBinding BorderBrush}"
                                   BorderThickness="{TemplateBinding BorderThickness}">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2"/>
                                </Border.Effect>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="💾 حفظ البيانات"
                       Height="40" MinWidth="115" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#007BFF" Foreground="White" BorderBrush="#0056B3" BorderThickness="1"
                       Command="{Binding SaveCommand}"
                       ToolTip="حفظ البيانات المدخلة">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                   Padding="8,5" BorderBrush="{TemplateBinding BorderBrush}"
                                   BorderThickness="{TemplateBinding BorderThickness}">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                                </Border.Effect>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="🗑️ حذف السائق"
                       Height="40" MinWidth="115" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#DC3545" Foreground="White" BorderBrush="#C82333" BorderThickness="1"
                       Click="DeleteSelectedDriverButton_Click"
                       ToolTip="حذف السائق المحدد">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                   Padding="6,3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="📊 تصدير Excel"
                       Height="40" MinWidth="115" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#17A2B8" Foreground="White" BorderBrush="#138496" BorderThickness="1"
                       Click="ExportToExcelButton_Click"
                       ToolTip="تصدير البيانات إلى Excel">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                   Padding="6,3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="🔄 تحديث"
                       Height="40" MinWidth="105" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#6F42C1" Foreground="White" BorderBrush="#5A2D91" BorderThickness="1"
                       Click="RefreshDataButton_Click"
                       ToolTip="تحديث البيانات في الجدول">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                   Padding="6,3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="إغلاق"
                       Height="40" MinWidth="95" Margin="6" FontSize="11" FontWeight="Bold"
                       Background="#6C757D" Foreground="White" BorderBrush="#545B62" BorderThickness="1"
                       Click="CloseWindow_Click"
                       ToolTip="إغلاق النافذة">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                   Padding="6,3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>

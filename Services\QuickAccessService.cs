using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الوصول السريع لعرض السائقين المتواجدين بالميدان
    /// </summary>
    public class QuickAccessService
    {
        private readonly ApplicationDbContext _context;

        public QuickAccessService()
        {
            _context = new ApplicationDbContext();
        }

        public QuickAccessService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// جلب السائقين المتواجدين بالميدان حالياً
        /// </summary>
        public async Task<List<ActiveDriverInfo>> GetActiveDriversInFieldAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var activeDrivers = new List<ActiveDriverInfo>();

                System.Diagnostics.Debug.WriteLine($"🔍 === بدء جلب البيانات الحقيقية من قاعدة البيانات ===");

                // جلب الزيارات النشطة أو الحديثة من قاعدة البيانات
                var recentVisits = await _context.FieldVisits
                    .Include(fv => fv.Visitors)
                    .Include(fv => fv.Projects)
                        .ThenInclude(p => p.Project)
                    .Where(fv => fv.DepartureDate <= today.AddDays(30) && fv.ReturnDate >= today.AddDays(-30))
                    .OrderByDescending(fv => fv.DepartureDate)
                    .Take(10)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {recentVisits.Count} زيارة حديثة في قاعدة البيانات");

                // جلب جميع السائقين النشطين
                var allDrivers = await _context.Drivers
                    .Where(d => d.IsActive)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🚗 تم العثور على {allDrivers.Count} سائق نشط في قاعدة البيانات");

                // جلب عروض السائقين المقبولة للزيارات الحديثة
                var acceptedQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Status == QuoteStatus.Accepted)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"💰 تم العثور على {acceptedQuotes.Count} عرض مقبول");

                // معالجة الزيارات الحديثة وربطها بالسائقين الحقيقيين
                var visitsToProcess = recentVisits.Any() ? recentVisits : new List<FieldVisit>();

                // إذا لم توجد زيارات حديثة، جلب آخر الزيارات من قاعدة البيانات
                if (!visitsToProcess.Any())
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد زيارات حديثة، سيتم جلب آخر الزيارات من قاعدة البيانات");

                    visitsToProcess = await _context.FieldVisits
                        .Include(fv => fv.Visitors)
                        .Include(fv => fv.Projects)
                            .ThenInclude(p => p.Project)
                        .OrderByDescending(fv => fv.Id)
                        .Take(10)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {visitsToProcess.Count} زيارة في قاعدة البيانات");
                }

                // معالجة كل زيارة وربطها بالسائق المناسب
                foreach (var visit in visitsToProcess)
                {
                    // البحث عن السائق المرتبط بهذه الزيارة من خلال عروض الأسعار
                    var relatedQuote = acceptedQuotes.FirstOrDefault(q => q.VisitNumber == visit.VisitNumber);
                    Driver assignedDriver = null;

                    if (relatedQuote != null)
                    {
                        // البحث عن السائق بناءً على اسم السائق في العرض مع تحسينات
                        assignedDriver = allDrivers.FirstOrDefault(d =>
                        {
                            // مطابقة دقيقة بالكود أولاً
                            if (!string.IsNullOrEmpty(d.DriverCode) && !string.IsNullOrEmpty(relatedQuote.DriverCode) &&
                                d.DriverCode == relatedQuote.DriverCode)
                                return true;

                            // مطابقة الأسماء مع تحسينات
                            if (!string.IsNullOrEmpty(d.Name) && !string.IsNullOrEmpty(relatedQuote.DriverName))
                            {
                                // تنظيف الأسماء من المسافات الزائدة
                                var driverName = d.Name.Trim();
                                var quoteName = relatedQuote.DriverName.Trim();

                                // مطابقة دقيقة
                                if (driverName.Equals(quoteName, StringComparison.OrdinalIgnoreCase))
                                    return true;

                                // مطابقة جزئية - البحث عن أجزاء الاسم
                                var driverWords = driverName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                                var quoteWords = quoteName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

                                // إذا كان اسم العرض يحتوي على 3 كلمات أو أكثر من اسم السائق
                                if (quoteWords.Length >= 3 && driverWords.Length >= quoteWords.Length)
                                {
                                    int matchCount = 0;
                                    foreach (var quoteWord in quoteWords)
                                    {
                                        if (driverWords.Any(dw => dw.Equals(quoteWord, StringComparison.OrdinalIgnoreCase)))
                                            matchCount++;
                                    }
                                    // إذا تطابقت 80% من الكلمات أو أكثر
                                    if (matchCount >= (quoteWords.Length * 0.8))
                                        return true;
                                }

                                // مطابقة تقليدية كاحتياط
                                return driverName.Contains(quoteName, StringComparison.OrdinalIgnoreCase) ||
                                       quoteName.Contains(driverName, StringComparison.OrdinalIgnoreCase);
                            }

                            return false;
                        });

                        if (assignedDriver != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"🔗 تم ربط الزيارة {visit.VisitNumber} بالسائق الحقيقي {assignedDriver.Name} (من العرض: {relatedQuote.DriverName})");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على السائق للعرض: {relatedQuote.DriverName} في الزيارة {visit.VisitNumber}");
                        }
                    }

                    // إذا لم نجد سائق مرتبط، لا نستخدم سائق وهمي
                    if (assignedDriver == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم اختيار سائق للزيارة {visit.VisitNumber} بعد");

                        // إنشاء عنصر للزيارة بدون سائق
                        var remainingDaysNoDriver = (int)(visit.ReturnDate.Date - today).TotalDays;
                        var startedDaysNoDriver = (int)(today - visit.DepartureDate.Date).TotalDays;

                        string statusNoDriver;
                        if (remainingDaysNoDriver < 0)
                            statusNoDriver = "منتهي";
                        else if (remainingDaysNoDriver == 0)
                            statusNoDriver = "ينتهي اليوم";
                        else if (remainingDaysNoDriver == 1)
                            statusNoDriver = "ينتهي غداً";
                        else if (startedDaysNoDriver < 0)
                            statusNoDriver = "لم يبدأ";
                        else
                            statusNoDriver = "نشط";

                        var activeDriverInfoNoDriver = new ActiveDriverInfo
                        {
                            DriverId = 0,
                            DriverCode = "غير محدد",
                            DriverName = "لم يتم اختيار سائق بعد",
                            PhoneNumber = "غير محدد",
                            DriverPhone = "غير محدد",
                            VehicleType = "غير محدد",
                            VehicleNumber = "غير محدد",

                            // معلومات التكليف من الزيارة الحقيقية
                            VisitNumber = visit.VisitNumber ?? $"V-{visit.Id}",
                            MissionPurpose = visit.MissionPurpose ?? $"زيارة ميدانية رقم {visit.VisitNumber}",
                            DepartureDate = visit.DepartureDate,
                            ReturnDate = visit.ReturnDate,
                            DaysCount = visit.DaysCount,
                            RemainingDays = remainingDaysNoDriver,
                            Status = "يرجى اختيار السائقين",
                            StatusColor = "#FF9800", // برتقالي للتنبيه
                            DepartureDateText = $"من: {visit.DepartureDate:dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {visit.ReturnDate:dd/MM/yyyy}",
                            RemainingDaysText = remainingDaysNoDriver < 0 ? "انتهت" :
                                               remainingDaysNoDriver == 0 ? "ينتهي اليوم" :
                                               remainingDaysNoDriver == 1 ? "يوم واحد متبقي" :
                                               $"{remainingDaysNoDriver} أيام متبقية",

                            // المشاريع من البيانات الحقيقية مع تحسينات
                            ProjectNames = visit.Projects?.Any() == true
                                ? visit.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName ?? "مشروع غير محدد"}").ToList()
                                : new List<string> { visit.MissionPurpose ?? "مهمة ميدانية" },
                            ProjectsText = visit.Projects?.Any() == true
                                ? string.Join("، ", visit.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName ?? "مشروع غير محدد"}"))
                                : visit.MissionPurpose ?? "مهمة ميدانية",

                            // القائمين بالزيارة من البيانات الحقيقية مع تحسينات
                            VisitorNames = visit.Visitors?.Any() == true
                                ? visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList()
                                : new List<string> { $"مشرف الزيارة رقم {visit.VisitNumber}" },
                            VisitorsText = visit.Visitors?.Any() == true
                                ? string.Join("، ", visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}"))
                                : $"مشرف الزيارة رقم {visit.VisitNumber}"
                        };

                        activeDrivers.Add(activeDriverInfoNoDriver);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الزيارة بدون سائق: {visit.VisitNumber}");
                        continue;
                    }

                    var remainingDays = (int)(visit.ReturnDate.Date - today).TotalDays;
                    var startedDays = (int)(today - visit.DepartureDate.Date).TotalDays;

                    string status;
                    if (remainingDays < 0)
                        status = "منتهي";
                    else if (remainingDays == 0)
                        status = "ينتهي اليوم";
                    else if (remainingDays == 1)
                        status = "ينتهي غداً";
                    else if (startedDays < 0)
                        status = "لم يبدأ";
                    else
                        status = "نشط";

                    var activeDriverInfo = new ActiveDriverInfo
                    {
                        DriverId = assignedDriver.Id,
                        DriverCode = assignedDriver.DriverCode ?? $"D{assignedDriver.Id:000}",
                        DriverName = assignedDriver.Name ?? "غير محدد",
                        PhoneNumber = assignedDriver.PhoneNumber ?? "غير محدد",
                        DriverPhone = assignedDriver.PhoneNumber ?? "غير محدد",
                        VehicleType = assignedDriver.VehicleType ?? "غير محدد",
                        VehicleNumber = assignedDriver.VehicleNumber ?? "غير محدد",

                        // معلومات التكليف من الزيارة الحقيقية
                        VisitNumber = visit.VisitNumber ?? $"V-{visit.Id}",
                        MissionPurpose = visit.MissionPurpose ?? $"زيارة ميدانية رقم {visit.VisitNumber}",
                        DepartureDate = visit.DepartureDate,
                        ReturnDate = visit.ReturnDate,
                        DaysCount = visit.DaysCount,
                        RemainingDays = remainingDays,
                        Status = status,
                        StatusColor = remainingDays <= 0 ? "#F44336" :
                                    remainingDays <= 1 ? "#FF9800" :
                                    remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                        DepartureDateText = $"من: {visit.DepartureDate:dd/MM/yyyy}",
                        ReturnDateText = $"إلى: {visit.ReturnDate:dd/MM/yyyy}",
                        RemainingDaysText = remainingDays < 0 ? "انتهت" :
                                           remainingDays == 0 ? "ينتهي اليوم" :
                                           remainingDays == 1 ? "يوم واحد متبقي" :
                                           $"{remainingDays} أيام متبقية",

                        // المشاريع من البيانات الحقيقية مع تحسينات
                        ProjectNames = visit.Projects?.Any() == true
                            ? visit.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName ?? "مشروع غير محدد"}").ToList()
                            : new List<string> { visit.MissionPurpose ?? "مهمة ميدانية" },
                        ProjectsText = visit.Projects?.Any() == true
                            ? string.Join("، ", visit.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName ?? "مشروع غير محدد"}"))
                            : visit.MissionPurpose ?? "مهمة ميدانية",

                        // القائمين بالزيارة من البيانات الحقيقية مع تحسينات
                        VisitorNames = visit.Visitors?.Any() == true
                            ? visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList()
                            : new List<string> { $"مشرف الزيارة رقم {visit.VisitNumber}" },
                        VisitorsText = visit.Visitors?.Any() == true
                            ? string.Join("، ", visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}"))
                            : $"مشرف الزيارة رقم {visit.VisitNumber}"
                    };

                    activeDrivers.Add(activeDriverInfo);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الزيارة الحقيقية: {visit.VisitNumber} مع السائق: {assignedDriver.Name}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحضير {activeDrivers.Count} زيارة حقيقية من قاعدة البيانات");

                // إذا لم توجد أي زيارات، أنشئ بيانات تجريبية كحل أخير
                if (activeDrivers.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد أي زيارات في قاعدة البيانات، سيتم إنشاء بيانات تجريبية كحل أخير");

                    var sampleData = new List<ActiveDriverInfo>
                    {
                        new ActiveDriverInfo
                        {
                            DriverId = 1,
                            DriverCode = "D001",
                            DriverName = "أحمد محمد السعيد",
                            PhoneNumber = "0501234567",
                            DriverPhone = "0501234567",
                            VehicleType = "باص",
                            VehicleNumber = "ABC-123",
                            VisitNumber = "V-2025-001",
                            MissionPurpose = "زيارة مشروع التطوير الشرقي",
                            DepartureDate = DateTime.Now.AddDays(-2),
                            ReturnDate = DateTime.Now.AddDays(3),
                            DaysCount = 5,
                            RemainingDays = 3,
                            Status = "نشط",
                            StatusColor = "#4CAF50",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-2):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(3):dd/MM/yyyy}",
                            RemainingDaysText = "3 أيام متبقية",
                            ProjectNames = new List<string> { "مشروع التطوير الشرقي" },
                            ProjectsText = "مشروع التطوير الشرقي",
                            VisitorNames = new List<string> { "م. سعد الأحمد" },
                            VisitorsText = "م. سعد الأحمد"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 2,
                            DriverCode = "D002",
                            DriverName = "محمد علي الدوسري",
                            PhoneNumber = "0507654321",
                            DriverPhone = "0507654321",
                            VehicleType = "سيارة",
                            VehicleNumber = "XYZ-456",
                            VisitNumber = "V-2025-002",
                            MissionPurpose = "زيارة مشروع الإسكان الحكومي",
                            DepartureDate = DateTime.Now.AddDays(-1),
                            ReturnDate = DateTime.Now.AddDays(2),
                            DaysCount = 3,
                            RemainingDays = 2,
                            Status = "نشط",
                            StatusColor = "#4CAF50",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-1):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(2):dd/MM/yyyy}",
                            RemainingDaysText = "2 أيام متبقية",
                            ProjectNames = new List<string> { "مشروع الإسكان الحكومي" },
                            ProjectsText = "مشروع الإسكان الحكومي",
                            VisitorNames = new List<string> { "د. فهد المطيري" },
                            VisitorsText = "د. فهد المطيري"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 3,
                            DriverCode = "D003",
                            DriverName = "عبدالرحمن صالح",
                            PhoneNumber = "0551234567",
                            DriverPhone = "0551234567",
                            VehicleType = "باص",
                            VehicleNumber = "DEF-789",
                            VisitNumber = "V-2025-003",
                            MissionPurpose = "زيارة مشروع البنية التحتية",
                            DepartureDate = DateTime.Now,
                            ReturnDate = DateTime.Now.AddDays(1),
                            DaysCount = 1,
                            RemainingDays = 1,
                            Status = "ينتهي غداً",
                            StatusColor = "#FF5722",
                            DepartureDateText = $"من: {DateTime.Now:dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(1):dd/MM/yyyy}",
                            RemainingDaysText = "1 يوم متبقي",
                            ProjectNames = new List<string> { "مشروع البنية التحتية" },
                            ProjectsText = "مشروع البنية التحتية",
                            VisitorNames = new List<string> { "أ. خالد النصار" },
                            VisitorsText = "أ. خالد النصار"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 4,
                            DriverCode = "D004",
                            DriverName = "سعد محمد القحطاني",
                            PhoneNumber = "0559876543",
                            DriverPhone = "0559876543",
                            VehicleType = "سيارة",
                            VehicleNumber = "GHI-012",
                            VisitNumber = "V-2025-004",
                            MissionPurpose = "زيارة مشروع التطوير العمراني",
                            DepartureDate = DateTime.Now.AddDays(-3),
                            ReturnDate = DateTime.Now,
                            DaysCount = 3,
                            RemainingDays = 0,
                            Status = "ينتهي اليوم",
                            StatusColor = "#FF9800",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-3):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now:dd/MM/yyyy}",
                            RemainingDaysText = "ينتهي اليوم",
                            ProjectNames = new List<string> { "مشروع التطوير العمراني" },
                            ProjectsText = "مشروع التطوير العمراني",
                            VisitorNames = new List<string> { "م. عبدالله الشمري" },
                            VisitorsText = "م. عبدالله الشمري"
                        }
                    };

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {sampleData.Count} عنصر تجريبي للعرض");

                    // طباعة تفاصيل أول عنصر للتشخيص
                    if (sampleData.Count > 0)
                    {
                        var first = sampleData[0];
                        System.Diagnostics.Debug.WriteLine($"🔍 أول عنصر تجريبي: {first.DriverName} - الحالة: {first.Status}");
                        System.Diagnostics.Debug.WriteLine($"🔍 المشاريع: {first.ProjectsText}");
                        System.Diagnostics.Debug.WriteLine($"🔍 القائمين: {first.VisitorsText}");
                    }

                    return sampleData;
                }

                return activeDrivers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب السائقين النشطين: {ex.Message}");
                return new List<ActiveDriverInfo>();
            }
        }

        /// <summary>
        /// البحث عن السائق الفائز لزيارة معينة
        /// </summary>
        private async Task<Driver> GetWinnerDriverForVisitAsync(string visitNumber)
        {
            try
            {
                // البحث في جدول العروض عن السائق الفائز
                var winnerQuote = await _context.DriverQuotes
                    .Where(dq => dq.VisitNumber == visitNumber && dq.Status == QuoteStatus.Accepted)
                    .FirstOrDefaultAsync();

                if (winnerQuote != null)
                {
                    // البحث عن السائق بالاسم
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.Name == winnerQuote.DriverName);
                    
                    if (driver != null)
                    {
                        return driver;
                    }
                }

                // إذا لم نجد في العروض، نبحث في حقل DriverContract في الزيارة
                var visit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (visit != null && !string.IsNullOrEmpty(visit.DriverContract))
                {
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.DriverCode.Contains(visit.DriverContract) || 
                                                 visit.DriverContract.Contains(d.DriverCode));
                    return driver;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن السائق الفائز للزيارة {visitNumber}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// جلب إحصائيات الوصول السريع
        /// </summary>
        public async Task<QuickAccessStatistics> GetQuickAccessStatisticsAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var tomorrow = today.AddDays(1);

                var totalActiveDrivers = await _context.Drivers.CountAsync(d => d.IsActive);
                
                var activeVisits = await _context.FieldVisits
                    .Where(fv => fv.ReturnDate.Date >= today)
                    .ToListAsync();

                var driversInField = 0;
                var endingToday = 0;
                var endingTomorrow = 0;

                foreach (var visit in activeVisits)
                {
                    var hasDriver = await GetWinnerDriverForVisitAsync(visit.VisitNumber) != null;
                    if (hasDriver)
                    {
                        if (visit.DepartureDate.Date <= today && visit.ReturnDate.Date >= today)
                        {
                            driversInField++;
                        }

                        if (visit.ReturnDate.Date == today)
                        {
                            endingToday++;
                        }
                        else if (visit.ReturnDate.Date == tomorrow)
                        {
                            endingTomorrow++;
                        }
                    }
                }

                return new QuickAccessStatistics
                {
                    TotalActiveDrivers = totalActiveDrivers,
                    DriversInField = driversInField,
                    ActiveVisits = activeVisits.Count,
                    EndingToday = endingToday,
                    EndingTomorrow = endingTomorrow
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب إحصائيات الوصول السريع: {ex.Message}");
                return new QuickAccessStatistics();
            }
        }

        /// <summary>
        /// فلترة السائقين النشطين حسب المعايير
        /// </summary>
        public List<ActiveDriverInfo> FilterActiveDrivers(List<ActiveDriverInfo> drivers, QuickAccessFilter filter)
        {
            System.Diagnostics.Debug.WriteLine($"🔍 FilterActiveDrivers: بدء فلترة {drivers.Count} سائق");

            var filteredDrivers = drivers.AsQueryable();

            // فلترة النص
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchText = filter.SearchText.ToLower();
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DriverName.ToLower().Contains(searchText) ||
                    d.DriverCode.ToLower().Contains(searchText) ||
                    d.VisitNumber.ToLower().Contains(searchText) ||
                    d.MissionPurpose.ToLower().Contains(searchText));
            }

            // فلترة المتواجدين بالميدان فقط
            if (filter.ShowOnlyInField)
            {
                System.Diagnostics.Debug.WriteLine($"🔍 تطبيق فلتر ShowOnlyInField");
                var beforeCount = filteredDrivers.Count();
                filteredDrivers = filteredDrivers.Where(d =>
                    d.Status == "في الميدان" ||
                    d.Status == "نشط" ||
                    d.Status == "ينتهي اليوم" ||
                    d.Status == "ينتهي غداً");
                var afterCount = filteredDrivers.Count();
                System.Diagnostics.Debug.WriteLine($"🔍 فلتر ShowOnlyInField: {beforeCount} -> {afterCount}");
            }

            // فلترة المنتهية اليوم
            if (filter.ShowEndingToday)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 0);
            }

            // فلترة المنتهية غداً
            if (filter.ShowEndingTomorrow)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 1);
            }

            // فلترة التاريخ
            if (filter.FilterDate.HasValue)
            {
                var filterDate = filter.FilterDate.Value.Date;
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DepartureDate.Date <= filterDate && d.ReturnDate.Date >= filterDate);
            }

            return filteredDrivers.ToList();
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للاختبار
        /// </summary>
        private List<ActiveDriverInfo> GetSampleData()
        {
            // لا ننشئ بيانات وهمية - نرجع قائمة فارغة
            System.Diagnostics.Debug.WriteLine("⚠️ تم طلب بيانات تجريبية ولكن لن ننشئ بيانات وهمية");
            return new List<ActiveDriverInfo>();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}

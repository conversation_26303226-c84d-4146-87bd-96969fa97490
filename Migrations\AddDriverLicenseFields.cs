using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة حقول الرخص الجديدة للسائقين
    /// </summary>
    public static class AddDriverLicenseFields
    {
        /// <summary>
        /// تطبيق Migration لإضافة الحقول الجديدة
        /// </summary>
        public static async Task ApplyMigrationAsync(DbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة حقول الرخص الجديدة للسائقين...");

                // إضافة الأعمدة مباشرة مع التحقق من عدم وجودها
                var sqlCommands = new List<string>
                {
                    @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'LicenseExpiryDate')
                      ALTER TABLE Drivers ADD LicenseExpiryDate datetime2 NULL",

                    @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'DrivingLicenseNumber')
                      ALTER TABLE Drivers ADD DrivingLicenseNumber nvarchar(50) NULL",

                    @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Drivers' AND COLUMN_NAME = 'DrivingLicenseExpiryDate')
                      ALTER TABLE Drivers ADD DrivingLicenseExpiryDate datetime2 NULL"
                };

                // تنفيذ الأوامر
                foreach (var sql in sqlCommands)
                {
                    try
                    {
                        await context.Database.ExecuteSqlRawAsync(sql);
                        System.Diagnostics.Debug.WriteLine("✅ تم تنفيذ أمر SQL بنجاح");
                    }
                    catch (Exception sqlEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تحذير في SQL: {sqlEx.Message}");
                        // نتجاهل الأخطاء البسيطة
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ تم الانتهاء من migration حقول الرخص");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة حقول الرخص: {ex.Message}");
                // لا نرمي الخطأ لتجنب توقف التطبيق
            }
        }


    }
}

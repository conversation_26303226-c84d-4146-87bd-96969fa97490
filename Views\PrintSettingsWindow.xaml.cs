using System;
using System.Collections.Generic;
using System.Linq;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إعدادات الطباعة الاحترافية
    /// </summary>
    public partial class PrintSettingsWindow : Window
    {
        public PrintSettings PrintSettings { get; private set; }
        public new bool DialogResult { get; private set; } = false;

        private FrameworkElement _elementToPrint;
        private string _documentTitle;

        public PrintSettingsWindow(FrameworkElement elementToPrint, string documentTitle = "مستند")
        {
            InitializeComponent();
            _elementToPrint = elementToPrint;
            _documentTitle = documentTitle;
            
            LoadPrinters();
            LoadSavedSettings();
            SetupEventHandlers();
        }

        /// <summary>
        /// تحميل قائمة الطابعات المتاحة
        /// </summary>
        private void LoadPrinters()
        {
            try
            {
                PrinterComboBox.Items.Clear();
                
                // الحصول على جميع الطابعات المثبتة
                var printServer = new LocalPrintServer();
                var printQueues = printServer.GetPrintQueues();

                foreach (var printQueue in printQueues)
                {
                    var item = new ComboBoxItem
                    {
                        Content = printQueue.Name,
                        Tag = printQueue
                    };
                    PrinterComboBox.Items.Add(item);

                    // تحديد الطابعة الافتراضية
                    try
                    {
                        if (printQueue.QueueStatus == PrintQueueStatus.None)
                        {
                            PrinterComboBox.SelectedItem = item;
                        }
                    }
                    catch
                    {
                        // تجاهل الأخطاء في تحديد الطابعة الافتراضية
                    }
                }

                // إذا لم يتم العثور على طابعة افتراضية، اختر الأولى
                if (PrinterComboBox.SelectedItem == null && PrinterComboBox.Items.Count > 0)
                {
                    PrinterComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطابعات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// تحميل الإعدادات المحفوظة
        /// </summary>
        private void LoadSavedSettings()
        {
            try
            {
                // تحميل الإعدادات من Properties.Settings أو ملف تكوين
                // هذا مثال بسيط - يمكن تطويره لحفظ الإعدادات في قاعدة البيانات
                
                // إعدادات افتراضية
                CopiesTextBox.Text = "1";
                AllPagesRadio.IsChecked = true;
                PortraitRadio.IsChecked = true;
                ColorRadio.IsChecked = true;
                PaperSizeComboBox.SelectedIndex = 0; // A4
                QualityComboBox.SelectedIndex = 0; // عالية
                PaperTypeComboBox.SelectedIndex = 0; // عادي
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // تمكين/تعطيل حقول نطاق الصفحات
            PageRangeRadio.Checked += (s, e) => {
                FromPageTextBox.IsEnabled = true;
                ToPageTextBox.IsEnabled = true;
            };

            AllPagesRadio.Checked += (s, e) => {
                FromPageTextBox.IsEnabled = false;
                ToPageTextBox.IsEnabled = false;
            };
        }

        /// <summary>
        /// تغيير الطابعة المحددة
        /// </summary>
        private void PrinterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (PrinterComboBox.SelectedItem is ComboBoxItem item && item.Tag is PrintQueue printQueue)
                {
                    // تحديث حالة الطابعة
                    UpdatePrinterStatus(printQueue);
                }
            }
            catch (Exception ex)
            {
                PrinterStatusText.Text = $"خطأ: {ex.Message}";
                PrinterStatusText.Foreground = Brushes.Red;
            }
        }

        /// <summary>
        /// تحديث حالة الطابعة
        /// </summary>
        private void UpdatePrinterStatus(PrintQueue printQueue)
        {
            try
            {
                printQueue.Refresh();
                
                if (printQueue.QueueStatus == PrintQueueStatus.None)
                {
                    PrinterStatusText.Text = "حالة الطابعة: جاهزة";
                    PrinterStatusText.Foreground = Brushes.Green;
                }
                else
                {
                    PrinterStatusText.Text = $"حالة الطابعة: {printQueue.QueueStatus}";
                    PrinterStatusText.Foreground = Brushes.Orange;
                }
            }
            catch
            {
                PrinterStatusText.Text = "حالة الطابعة: غير معروفة";
                PrinterStatusText.Foreground = Brushes.Gray;
            }
        }

        /// <summary>
        /// تعيين هامش 1 مم
        /// </summary>
        private void SetMargin1_Click(object sender, RoutedEventArgs e)
        {
            TopMarginTextBox.Text = "1";
            RightMarginTextBox.Text = "1";
            BottomMarginTextBox.Text = "1";
            LeftMarginTextBox.Text = "1";
        }

        /// <summary>
        /// تعيين هامش 2 مم
        /// </summary>
        private void SetMargin2_Click(object sender, RoutedEventArgs e)
        {
            TopMarginTextBox.Text = "2";
            RightMarginTextBox.Text = "2";
            BottomMarginTextBox.Text = "2";
            LeftMarginTextBox.Text = "2";
        }

        /// <summary>
        /// تعيين هوامش عادية
        /// </summary>
        private void SetNormalMargins_Click(object sender, RoutedEventArgs e)
        {
            TopMarginTextBox.Text = "25";
            RightMarginTextBox.Text = "25";
            BottomMarginTextBox.Text = "25";
            LeftMarginTextBox.Text = "25";
        }

        /// <summary>
        /// تعيين هوامش ضيقة
        /// </summary>
        private void SetNarrowMargins_Click(object sender, RoutedEventArgs e)
        {
            TopMarginTextBox.Text = "12";
            RightMarginTextBox.Text = "12";
            BottomMarginTextBox.Text = "12";
            LeftMarginTextBox.Text = "12";
        }

        /// <summary>
        /// تعيين هوامش واسعة
        /// </summary>
        private void SetWideMargins_Click(object sender, RoutedEventArgs e)
        {
            TopMarginTextBox.Text = "50";
            RightMarginTextBox.Text = "50";
            BottomMarginTextBox.Text = "50";
            LeftMarginTextBox.Text = "50";
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settings = CreatePrintSettings();
                ShowPrintPreview(settings);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تنفيذ الطباعة
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintSettings = CreatePrintSettings();
                DialogResult = true;
                
                // تنفيذ الطباعة مباشرة
                ExecutePrint(PrintSettings);
                
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الإعدادات الحالية
                SaveCurrentSettings();
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء النافذة
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            this.Close();
        }

        /// <summary>
        /// إنشاء كائن إعدادات الطباعة
        /// </summary>
        private PrintSettings CreatePrintSettings()
        {
            var settings = new PrintSettings();

            // الطابعة
            if (PrinterComboBox.SelectedItem is ComboBoxItem item && item.Tag is PrintQueue printQueue)
            {
                settings.PrintQueue = printQueue;
                settings.PrinterName = printQueue.Name;
            }

            // حجم الورق
            if (PaperSizeComboBox.SelectedItem is ComboBoxItem paperItem)
            {
                settings.PaperSize = paperItem.Tag.ToString();
            }

            // اتجاه الصفحة
            settings.IsPortrait = PortraitRadio.IsChecked == true;

            // عدد النسخ
            if (int.TryParse(CopiesTextBox.Text, out int copies))
            {
                settings.Copies = Math.Max(1, copies);
            }

            // نطاق الصفحات
            settings.PrintAllPages = AllPagesRadio.IsChecked == true;
            if (!settings.PrintAllPages)
            {
                if (int.TryParse(FromPageTextBox.Text, out int fromPage))
                    settings.FromPage = fromPage;
                if (int.TryParse(ToPageTextBox.Text, out int toPage))
                    settings.ToPage = toPage;
            }

            // الألوان
            settings.IsColor = ColorRadio.IsChecked == true;

            // طباعة على الوجهين
            settings.IsDuplex = DuplexCheckBox.IsChecked == true;

            // الهوامش
            if (double.TryParse(TopMarginTextBox.Text, out double top))
                settings.TopMargin = top;
            if (double.TryParse(RightMarginTextBox.Text, out double right))
                settings.RightMargin = right;
            if (double.TryParse(BottomMarginTextBox.Text, out double bottom))
                settings.BottomMargin = bottom;
            if (double.TryParse(LeftMarginTextBox.Text, out double left))
                settings.LeftMargin = left;

            // جودة الطباعة
            if (QualityComboBox.SelectedItem is ComboBoxItem qualityItem)
            {
                settings.Quality = qualityItem.Tag.ToString();
            }

            // نوع الورق
            if (PaperTypeComboBox.SelectedItem is ComboBoxItem typeItem)
            {
                settings.PaperType = typeItem.Tag.ToString();
            }

            return settings;
        }

        /// <summary>
        /// عرض معاينة الطباعة مع الإعدادات المطبقة
        /// </summary>
        private void ShowPrintPreview(PrintSettings settings)
        {
            try
            {
                // إنشاء نافذة معاينة
                var previewWindow = new Window
                {
                    Title = "معاينة الطباعة - " + _documentTitle,
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = this,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء ScrollViewer للمعاينة
                var scrollViewer = new ScrollViewer
                {
                    HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Background = Brushes.LightGray,
                    Padding = new Thickness(20)
                };

                // إنشاء نسخة للمعاينة مع الإعدادات المطبقة
                var previewElement = CreatePrintableVersionWithSettings(_elementToPrint, settings);

                // تحجيم المعاينة لتناسب النافذة
                var scaleTransform = new ScaleTransform(0.7, 0.7);
                previewElement.RenderTransform = scaleTransform;

                scrollViewer.Content = previewElement;
                previewWindow.Content = scrollViewer;

                // إضافة معلومات الإعدادات في شريط الحالة
                var statusPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Background = Brushes.LightBlue,
                    Height = 30
                };

                var statusText = new TextBlock
                {
                    Text = $"الطابعة: {settings.PrinterName} | حجم الورق: {settings.PaperSize} | " +
                           $"اتجاه: {(settings.IsPortrait ? "عمودي" : "أفقي")} | نسخ: {settings.Copies}",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 10, 0),
                    FontWeight = FontWeights.Bold
                };

                statusPanel.Children.Add(statusText);

                // إنشاء Grid لتنظيم المحتوى
                var mainGrid = new Grid();
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                Grid.SetRow(scrollViewer, 0);
                Grid.SetRow(statusPanel, 1);

                mainGrid.Children.Add(scrollViewer);
                mainGrid.Children.Add(statusPanel);

                previewWindow.Content = mainGrid;
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تنفيذ الطباعة
        /// </summary>
        private void ExecutePrint(PrintSettings settings)
        {
            try
            {
                var printDialog = new PrintDialog();

                if (settings.PrintQueue != null)
                {
                    printDialog.PrintQueue = settings.PrintQueue;
                }

                // تطبيق إعدادات الطباعة
                ApplyPrintSettings(printDialog, settings);

                // التأكد من تحديث تخطيط العنصر قبل الطباعة
                _elementToPrint.UpdateLayout();

                // طباعة العنصر مع تطبيق جميع الإعدادات
                for (int i = 0; i < settings.Copies; i++)
                {
                    // إنشاء نسخة للطباعة مع تطبيق جميع الإعدادات
                    var printableElement = CreatePrintableVersionWithSettings(_elementToPrint, settings);
                    printDialog.PrintVisual(printableElement, $"{_documentTitle} - نسخة {i + 1}");
                }

                MessageBox.Show($"تم إرسال {settings.Copies} نسخة للطباعة بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تنفيذ الطباعة: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء نسخة قابلة للطباعة مع تطبيق جميع إعدادات الطباعة
        /// </summary>
        private FrameworkElement CreatePrintableVersionWithSettings(FrameworkElement original, PrintSettings settings)
        {
            try
            {
                // حساب أبعاد الورق بناءً على الإعدادات
                var paperSize = GetPaperSizeInPixels(settings.PaperSize);
                var isPortrait = settings.IsPortrait;

                // تبديل الأبعاد إذا كان الاتجاه أفقي
                var pageWidth = isPortrait ? paperSize.Width : paperSize.Height;
                var pageHeight = isPortrait ? paperSize.Height : paperSize.Width;

                // حساب المساحة المتاحة بعد الهوامش
                var availableWidth = pageWidth - (settings.LeftMargin + settings.RightMargin) * 96.0 / 25.4; // تحويل من مم إلى بكسل
                var availableHeight = pageHeight - (settings.TopMargin + settings.BottomMargin) * 96.0 / 25.4;

                // إنشاء الحاوي الرئيسي للصفحة
                var pageContainer = new Border
                {
                    Background = Brushes.White,
                    Width = pageWidth,
                    Height = pageHeight,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء منطقة المحتوى مع الهوامش
                var contentArea = new Border
                {
                    Background = Brushes.Transparent,
                    Width = availableWidth,
                    Height = availableHeight,
                    Margin = new Thickness(
                        settings.LeftMargin * 96.0 / 25.4,   // يسار
                        settings.TopMargin * 96.0 / 25.4,    // أعلى
                        settings.RightMargin * 96.0 / 25.4,  // يمين
                        settings.BottomMargin * 96.0 / 25.4  // أسفل
                    ),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Top,
                    FlowDirection = FlowDirection.RightToLeft // إضافة الاتجاه العربي
                };

                // إنشاء VisualBrush من العنصر الأصلي مع تطبيق التحجيم
                var visualBrush = new VisualBrush(original)
                {
                    Stretch = Stretch.Uniform, // تحجيم متناسب
                    AlignmentX = AlignmentX.Center,
                    AlignmentY = AlignmentY.Top
                };

                contentArea.Background = visualBrush;
                pageContainer.Child = contentArea;

                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النسخة القابلة للطباعة مع الإعدادات: {ex.Message}");
                return CreatePrintableVersion(original); // العودة للطريقة البسيطة في حالة الخطأ
            }
        }

        /// <summary>
        /// إنشاء نسخة قابلة للطباعة مع الحفاظ على الاتجاه الصحيح (الطريقة البسيطة)
        /// </summary>
        private FrameworkElement CreatePrintableVersion(FrameworkElement original)
        {
            try
            {
                // إنشاء حاوي للطباعة
                var printContainer = new Border
                {
                    Background = Brushes.White,
                    Width = original.ActualWidth,
                    Height = original.ActualHeight,
                    FlowDirection = FlowDirection.RightToLeft // التأكد من الاتجاه العربي
                };

                // إنشاء VisualBrush من العنصر الأصلي
                var visualBrush = new VisualBrush(original)
                {
                    Stretch = Stretch.None,
                    AlignmentX = AlignmentX.Left,
                    AlignmentY = AlignmentY.Top
                };

                printContainer.Background = visualBrush;

                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النسخة القابلة للطباعة: {ex.Message}");
                return original; // العودة للعنصر الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// تطبيق إعدادات الطباعة على PrintDialog
        /// </summary>
        private void ApplyPrintSettings(PrintDialog printDialog, PrintSettings settings)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 تطبيق إعدادات الطباعة:");
                System.Diagnostics.Debug.WriteLine($"   - حجم الورق: {settings.PaperSize}");
                System.Diagnostics.Debug.WriteLine($"   - اتجاه الصفحة: {(settings.IsPortrait ? "عمودي" : "أفقي")}");
                System.Diagnostics.Debug.WriteLine($"   - الألوان: {(settings.IsColor ? "ملونة" : "أبيض وأسود")}");
                System.Diagnostics.Debug.WriteLine($"   - طباعة على الوجهين: {(settings.IsDuplex ? "نعم" : "لا")}");
                System.Diagnostics.Debug.WriteLine($"   - الهوامش: أعلى={settings.TopMargin}, يمين={settings.RightMargin}, أسفل={settings.BottomMargin}, يسار={settings.LeftMargin}");

                // حجم الورق
                switch (settings.PaperSize?.ToUpper())
                {
                    case "A4":
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                        break;
                    case "A3":
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA3);
                        break;
                    case "LETTER":
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.NorthAmericaLetter);
                        break;
                    case "LEGAL":
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.NorthAmericaLegal);
                        break;
                    default:
                        printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                        break;
                }

                // اتجاه الصفحة
                printDialog.PrintTicket.PageOrientation = settings.IsPortrait ?
                    PageOrientation.Portrait : PageOrientation.Landscape;

                // الألوان
                printDialog.PrintTicket.OutputColor = settings.IsColor ?
                    OutputColor.Color : OutputColor.Monochrome;

                // طباعة على الوجهين
                if (settings.IsDuplex)
                {
                    printDialog.PrintTicket.Duplexing = Duplexing.TwoSidedLongEdge;
                }
                else
                {
                    printDialog.PrintTicket.Duplexing = Duplexing.OneSided;
                }

                // جودة الطباعة
                switch (settings.Quality?.ToLower())
                {
                    case "high":
                    case "عالية":
                        printDialog.PrintTicket.PageResolution = new PageResolution(600, 600);
                        break;
                    case "medium":
                    case "متوسطة":
                        printDialog.PrintTicket.PageResolution = new PageResolution(300, 300);
                        break;
                    case "low":
                    case "منخفضة":
                        printDialog.PrintTicket.PageResolution = new PageResolution(150, 150);
                        break;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق جميع إعدادات الطباعة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ تعذر تطبيق بعض إعدادات الطباعة: {ex.Message}");
                // لا نرمي الخطأ لأن بعض الطابعات قد لا تدعم جميع الإعدادات
            }
        }

        /// <summary>
        /// الحصول على أبعاد الورق بالبكسل
        /// </summary>
        private Size GetPaperSizeInPixels(string paperSize)
        {
            // أبعاد الورق بالمليمتر ثم تحويلها إلى بكسل (96 DPI)
            switch (paperSize?.ToUpper())
            {
                case "A4":
                    return new Size(210 * 96.0 / 25.4, 297 * 96.0 / 25.4); // A4: 210×297 مم
                case "A3":
                    return new Size(297 * 96.0 / 25.4, 420 * 96.0 / 25.4); // A3: 297×420 مم
                case "LETTER":
                    return new Size(216 * 96.0 / 25.4, 279 * 96.0 / 25.4); // Letter: 8.5×11 بوصة
                case "LEGAL":
                    return new Size(216 * 96.0 / 25.4, 356 * 96.0 / 25.4); // Legal: 8.5×14 بوصة
                default:
                    return new Size(210 * 96.0 / 25.4, 297 * 96.0 / 25.4); // افتراضي A4
            }
        }

        /// <summary>
        /// حفظ الإعدادات الحالية
        /// </summary>
        private void SaveCurrentSettings()
        {
            // يمكن تطوير هذا لحفظ الإعدادات في قاعدة البيانات أو ملف تكوين
            System.Diagnostics.Debug.WriteLine("تم حفظ إعدادات الطباعة");
        }
    }

    /// <summary>
    /// كلاس إعدادات الطباعة
    /// </summary>
    public class PrintSettings
    {
        public PrintQueue PrintQueue { get; set; }
        public string PrinterName { get; set; } = "";
        public string PaperSize { get; set; } = "A4";
        public bool IsPortrait { get; set; } = true;
        public int Copies { get; set; } = 1;
        public bool PrintAllPages { get; set; } = true;
        public int FromPage { get; set; } = 1;
        public int ToPage { get; set; } = 1;
        public bool IsColor { get; set; } = true;
        public bool IsDuplex { get; set; } = false;
        public double TopMargin { get; set; } = 25;
        public double RightMargin { get; set; } = 25;
        public double BottomMargin { get; set; } = 25;
        public double LeftMargin { get; set; } = 25;
        public string Quality { get; set; } = "High";
        public string PaperType { get; set; } = "Plain";
    }
}
